# Changelog

This document records all significant changes to the Micro-Core project, including new features, improvements, fixes, and breaking changes.

## Version Guidelines

We follow [Semantic Versioning](https://semver.org/) (SemVer) for version management:

- **Major Version** (X.0.0): Breaking changes
- **Minor Version** (0.X.0): New features (backward compatible)
- **Patch Version** (0.0.X): Bug fixes (backward compatible)

## [0.1.0] - 2025-07-26

### 🎉 Initial Release

This is the first version of Micro-Core, providing a complete micro-frontend architecture solution.

#### ✨ New Features

**Core Architecture**
- 🏗️ Micro-kernel architecture design, core library under 15KB
- 🔌 100% plugin-based architecture with on-demand loading
- 🎯 Complete application lifecycle management
- 📦 Unified application registration and management system

**Sandbox System**
- 🛡️ Proxy Sandbox - High-performance JavaScript isolation
- 🖼️ Iframe Sandbox - Strongest isolation level
- 🧩 WebComponent Sandbox - Style isolation
- 📝 DefineProperty Sandbox - Compatibility sandbox
- 🏷️ Namespace Sandbox - Lightweight isolation
- 🔗 Federated Component Sandbox - Module federation support

**Routing System**
- 🧭 Unified routing management and coordination
- 🔄 Dynamic routing and nested routing support
- 🛡️ Route guards and permission control
- 💾 Route caching and preloading
- 🎨 Route transition animations

**Communication System**
- 📡 EventBus - High-performance event bus
- 🌐 GlobalState - Reactive global state management
- 💬 Direct Communication - Direct messaging between applications
- 🔄 State synchronization and persistence
- 🎛️ Communication middleware support

**Framework Adapters**
- ⚛️ React Adapter (16.8+/17.x/18.x)
- 💚 Vue Adapter (2.7+/3.x)
- 🅰️ Angular Adapter (12+)
- 🔥 Svelte Adapter
- 💎 Solid.js Adapter
- 📄 Native HTML/JS Adapter

**Build Tool Integration**
- ⚡ Vite 7.0.6 deep integration
- 📦 Webpack 5.x support
- 🎯 Rollup 4.x support
- 🚀 esbuild 0.19.x support
- 📊 Rspack 0.4.x support
- 📋 Parcel zero-config support
- 🔥 Turbopack experimental support

**High-Performance Loaders**
- 👷 Worker Loader - Background resource loading
- 🔧 WebAssembly Loader - Native performance computing
- 🧠 Smart Preloading - Route-based prediction
- 💾 Multi-layer caching strategy
- 📊 Performance monitoring and optimization

**Compatibility Plugins**
- 🔄 qiankun compatibility plugin - Seamless migration support
- 🌊 Wujie compatibility plugin - iframe and WebComponent integration
- 📋 API comparison table and migration guide
- 🛠️ Automated migration tools

#### 🔧 Developer Experience

**Development Tools**
- 🎛️ Developer panel and debugging tools
- 📊 Real-time performance monitoring
- 🐛 Error tracking and recovery
- 📝 Detailed logging system
- 🔍 Application state visualization

**Testing Support**
- 🧪 Complete testing toolchain
- 📋 Unit tests, integration tests, E2E tests
- 🎯 100% test coverage
- 🔄 Automated testing workflow

**Documentation System**
- 📚 Documentation system based on VitePress 2.0.0-alpha.8
- 🌐 Complete Chinese documentation
- 💡 Rich examples and best practices
- 🎮 Online playground support

#### 🏗️ Architecture Features

**Sidecar Pattern**
- 🚀 One-line code to access micro-frontend
- 🔧 Zero-configuration startup
- 📦 Automatic configuration detection
- 🔄 Progressive migration support

**Plugin Ecosystem**
- 🔌 Router Plugin - Unified routing management
- 💬 Communication Plugin - Inter-application communication
- 🔐 Authentication Plugin - Unified identity authentication
- 📊 Monitoring Plugin - Performance and error monitoring
- 📝 Logging Plugin - Unified log management
- 🎯 Custom plugin development support

#### 📦 Package Management

**NPM Package Structure**
- `@micro-core/core` - Core runtime
- `@micro-core/sidecar` - Sidecar pattern entry
- `@micro-core/plugin-*` - Official plugin collection
- `@micro-core/adapter-*` - Framework adapters
- `@micro-core/builder-*` - Build tool adapters

**Version Management**
- 📋 Semantic version control
- 🔄 Automated release process
- 📊 Automatic changelog generation
- 🎯 Backward compatibility guarantee

#### 🌟 Technical Highlights

**Performance Optimization**
- ⚡ Micro-kernel design, startup speed < 100ms
- 💾 Smart caching, reduce duplicate loading
- 🔄 On-demand loading, reduce initial bundle size
- 📊 Performance monitoring, real-time optimization suggestions

**Security Features**
- 🛡️ Multi-layer sandbox isolation
- 🔐 CSP Content Security Policy
- 🚫 XSS and CSRF protection
- 🔒 Permission control and access management

**Scalability**
- 🔌 Plugin architecture, on-demand feature composition
- 🎯 Rich hook system
- 🔧 Custom adapter development
- 📦 Modular design, easy to maintain

#### 🎯 Use Cases

- 🏢 Micro-frontend transformation of large enterprise applications
- 🔄 Progressive upgrade of legacy systems
- 🌐 Collaborative development of multi-tech stack teams
- 📊 Plugin-based expansion of platform products
- 🎮 Architecture optimization of complex frontend applications

#### 📚 Documentation and Examples

**Complete Documentation**
- 📖 Detailed usage guides and API documentation
- 🎯 Best practices and architecture design guides
- 🔄 Complete migration guide (qiankun/wujie)
- 🧪 Testing strategies and deployment guides

**Rich Examples**
- ⚛️ React 18 micro-application examples
- 💚 Vue 2/3 micro-application examples
- 🅰️ Angular 16+ micro-application examples
- 🔥 Svelte micro-application examples
- 💎 Solid.js micro-application examples
- 📄 Native HTML/JS micro-application examples

#### 🤝 Community Support

- 🐙 [GitHub Repository](https://github.com/echo008/micro-core)
- 📦 [NPM Organization](https://www.npmjs.com/org/micro-core)
- 📚 [Online Documentation](https://micro-core.dev)
- 💬 [Issue Feedback](https://github.com/echo008/micro-core/issues)
- 🎮 [Online Playground](https://playground.micro-core.dev)

---

## 🔮 Future Roadmap

### v0.2.0 (Planned)

**Enhanced Features**
- 🎨 Visual configuration tool
- 📊 Richer performance monitoring
- 🔧 More build tool support
- 🌐 Internationalization support

**New Features**
- 🎯 Micro-application lazy loading optimization
- 🔄 Hot reload support
- 📱 Mobile optimization
- 🎮 More examples and templates

### v1.0.0 (Long-term Goal)

**Production Ready**
- 🏢 Enterprise-level feature completion
- 🔒 Enhanced security
- 📊 Monitoring and alerting system
- 🎯 Performance optimization tools

**Ecosystem Building**
- 🔌 Richer plugin ecosystem
- 🛠️ Complete development toolchain
- 📚 Community documentation and tutorials
- 🎓 Training and certification system

---

## 📄 License

This project is open source under the [MIT License](https://github.com/echo008/micro-core/blob/main/LICENSE).

## 🙏 Acknowledgments

Thanks to all developers and community members who contributed to the Micro-Core project.

---

**Download and Use**

```bash
# Install core package
npm install @micro-core/core

# Or use yarn
yarn add @micro-core/core

# Or use pnpm
pnpm add @micro-core/core
```

Start your micro-frontend journey! 🚀