{"name": "@micro-core/adapter-svelte", "version": "0.1.0", "description": "Micro-Core Svelte 适配器 - 支持 Svelte 框架的微前端集成", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "require": "./dist/index.js", "types": "./dist/index.d.ts"}}, "files": ["dist"], "scripts": {"build": "vite build", "dev": "vite build --watch", "test": "vitest", "test:coverage": "vitest --coverage", "type-check": "tsc --noEmit"}, "keywords": ["micro-frontend", "svelte", "adapter", "micro-core"], "author": "Echo <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/echo008/micro-core.git", "directory": "packages/adapters/adapter-svelte"}, "peerDependencies": {"@micro-core/core": "workspace:*", "svelte": ">=4.0.0"}, "devDependencies": {"@micro-core/core": "workspace:*", "@types/node": "^20.0.0", "typescript": "^5.3.0", "vite": "^7.0.6", "vitest": "^3.2.4"}, "engines": {"node": ">=18.0.0"}}