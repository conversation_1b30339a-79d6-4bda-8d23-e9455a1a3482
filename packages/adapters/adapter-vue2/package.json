{"name": "@micro-core/adapter-vue2", "version": "0.1.0", "description": "Vue 2.x framework adapter for Micro-Core micro-frontend architecture", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "vite build", "dev": "vite build --watch", "test": "vitest", "test:coverage": "vitest --coverage", "lint": "eslint src --ext .ts,.vue", "lint:fix": "eslint src --ext .ts,.vue --fix", "type-check": "tsc --noEmit"}, "keywords": ["micro-frontend", "vue", "vue2", "adapter", "micro-core"], "author": "Echo <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/echo008/micro-core.git", "directory": "packages/adapters/adapter-vue2"}, "dependencies": {"@micro-core/core": "workspace:*", "@micro-core/shared-types": "workspace:*", "@micro-core/adapter-shared": "workspace:*"}, "peerDependencies": {"vue": "^2.6.0 || ^2.7.0"}, "devDependencies": {"@types/node": "^20.0.0", "typescript": "^5.3.0", "vite": "^7.0.6", "vitest": "^3.2.4", "vue": "^2.7.16"}, "publishConfig": {"access": "public"}}