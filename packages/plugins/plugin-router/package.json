{"name": "@micro-core/plugin-router", "version": "0.1.0", "description": "Micro-Core 路由管理插件", "type": "module", "main": "dist/index.js", "module": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}}, "files": ["dist"], "scripts": {"build": "vite build", "dev": "vite build --watch", "test": "vitest", "test:coverage": "vitest --coverage", "type-check": "tsc --noEmit"}, "keywords": ["micro-frontend", "router", "plugin", "micro-core"], "author": "Echo <<EMAIL>>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/echo008/micro-core.git", "directory": "packages/plugins/plugin-router"}, "peerDependencies": {"@micro-core/core": "workspace:*"}, "devDependencies": {"@micro-core/core": "workspace:*", "@micro-core/shared": "workspace:*", "typescript": "^5.3.0", "vite": "^7.0.6", "vitest": "^3.2.4"}}