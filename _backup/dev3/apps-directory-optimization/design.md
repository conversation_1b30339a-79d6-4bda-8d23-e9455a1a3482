# Design Document

## Overview

This design document outlines the comprehensive architecture and implementation strategy for optimizing the `/apps` directory of the Micro-Core micro-frontend project. The design addresses the identified issues in directory structure, code quality, testing coverage, and architectural compliance while establishing a robust foundation for scalable micro-frontend development.

## Architecture

### Current State Analysis

The current `/apps` directory structure has several architectural inconsistencies:

1. **Redundant Structure**: The `apps/examples/` directory duplicates functionality found in other sub-applications
2. **Inconsistent Implementation**: Applications don't consistently implement micro-frontend lifecycle hooks
3. **Missing Testing**: No test files exist across any applications
4. **Configuration Drift**: Build configurations vary significantly between applications
5. **Dependency Inconsistencies**: Package versions and workspace references are inconsistent

### Target Architecture

The optimized architecture will follow a layered approach:

```
┌─────────────────────────────────────────────────────────────────┐
│                    Application Layer                            │
├─────────────────────────────────────────────────────────────────┤
│  Main Applications          │  Sub Applications                 │
│  ├── main-app-vite         │  ├── sub-app-react               │
│  └── playground            │  ├── sub-app-vue2                │
│                             │  ├── sub-app-vue3                │
│                             │  ├── sub-app-angular             │
│                             │  ├── sub-app-svelte              │
│                             │  ├── sub-app-solid               │
│                             │  └── sub-app-html                │
├─────────────────────────────────────────────────────────────────┤
│                    Integration Layer                            │
├─────────────────────────────────────────────────────────────────┤
│  Framework Adapters         │  Build Tools                     │
│  ├── @micro-core/adapter-* │  ├── @micro-core/builder-vite    │
│  └── Lifecycle Management  │  └── Configuration Standards     │
├─────────────────────────────────────────────────────────────────┤
│                    Core Layer                                   │
├─────────────────────────────────────────────────────────────────┤
│  @micro-core/core          │  @micro-core/sidecar             │
│  Plugin System             │  Zero-config Entry               │
└─────────────────────────────────────────────────────────────────┘
```

## Components and Interfaces

### 1. Standardized Application Structure

Each application will follow a consistent internal structure:

```typescript
interface ApplicationStructure {
  src: {
    main: string;           // Entry point with lifecycle exports
    App: string;            // Main application component
    components: Directory;  // Reusable components
    utils: Directory;       // Utility functions
    styles: Directory;      // Application styles
    types: Directory;       // TypeScript type definitions
    __tests__: Directory;   // Test files
  };
  public: Directory;        // Static assets
  config: {
    'package.json': PackageConfig;
    'vite.config.ts': ViteConfig;
    'tsconfig.json': TypeScriptConfig;
    'vitest.config.ts': VitestConfig;
  };
}
```

### 2. Micro-Frontend Lifecycle Interface

All sub-applications must implement the standard lifecycle interface:

```typescript
interface MicroFrontendLifecycle {
  bootstrap(props: MicroAppProps): Promise<void>;
  mount(props: MicroAppProps): Promise<void>;
  unmount(props: MicroAppProps): Promise<void>;
  update?(props: MicroAppProps): Promise<void>;
}

interface MicroAppProps {
  container: HTMLElement;
  basename?: string;
  theme?: 'light' | 'dark';
  user?: UserContext;
  [key: string]: any;
}
```

### 3. Build Configuration Interface

Standardized build configuration for all applications:

```typescript
interface StandardBuildConfig {
  vite: {
    plugins: VitePlugin[];
    server: ServerConfig;
    build: BuildConfig;
    define: Record<string, any>;
  };
  typescript: {
    strict: true;
    target: 'ES2020';
    moduleResolution: 'bundler';
  };
  vitest: {
    environment: 'jsdom';
    coverage: CoverageConfig;
    setupFiles: string[];
  };
}
```

### 4. Testing Architecture

Comprehensive testing strategy with three layers:

```typescript
interface TestingArchitecture {
  unit: {
    framework: 'vitest';
    coverage: 100;
    files: '**/*.test.{ts,tsx}';
  };
  integration: {
    scope: 'micro-frontend-lifecycle';
    files: '**/*.integration.test.{ts,tsx}';
  };
  e2e: {
    framework: 'playwright';
    scope: 'cross-application-workflows';
    files: '**/*.e2e.test.{ts,tsx}';
  };
}
```

## Data Models

### 1. Application Registry Model

```typescript
interface ApplicationRegistry {
  applications: Map<string, ApplicationConfig>;
  mainApps: ApplicationConfig[];
  subApps: ApplicationConfig[];
}

interface ApplicationConfig {
  name: string;
  type: 'main' | 'sub';
  framework: 'react' | 'vue2' | 'vue3' | 'angular' | 'svelte' | 'solid' | 'html';
  entry: string;
  port: number;
  dependencies: DependencyConfig;
  buildConfig: BuildConfiguration;
  testConfig: TestConfiguration;
}
```

### 2. Dependency Management Model

```typescript
interface DependencyConfig {
  workspace: Record<string, 'workspace:*'>;
  external: Record<string, string>;
  devDependencies: Record<string, string>;
  peerDependencies?: Record<string, string>;
}

interface DependencyStandards {
  vite: '^7.0.6';
  typescript: '^5.3.0';
  vitest: '^3.2.4';
  frameworks: {
    react: '^18.2.0';
    vue: '^3.4.0';
    angular: '^16.0.0';
  };
}
```

### 3. Build Output Model

```typescript
interface BuildOutput {
  main: {
    format: 'es' | 'umd';
    fileName: string;
    exports: MicroFrontendLifecycle;
  };
  assets: {
    css: string[];
    images: string[];
    fonts: string[];
  };
  manifest: {
    name: string;
    version: string;
    dependencies: string[];
    exports: string[];
  };
}
```

## Error Handling

### 1. Application-Level Error Handling

```typescript
interface ErrorHandlingStrategy {
  errorBoundaries: {
    react: 'React.ErrorBoundary';
    vue: 'Vue.errorHandler';
    angular: 'ErrorHandler';
  };
  
  lifecycleErrors: {
    bootstrap: 'retry-with-fallback';
    mount: 'graceful-degradation';
    unmount: 'force-cleanup';
  };
  
  buildErrors: {
    typescript: 'fail-fast';
    bundling: 'detailed-reporting';
    testing: 'continue-on-non-critical';
  };
}
```

### 2. Cross-Application Error Recovery

```typescript
interface ErrorRecoveryMechanism {
  isolation: 'sandbox-per-application';
  fallback: 'static-error-page';
  reporting: {
    console: boolean;
    monitoring: 'optional';
    user: 'user-friendly-message';
  };
  recovery: {
    automatic: 'retry-failed-mounts';
    manual: 'reload-application-button';
  };
}
```

## Testing Strategy

### 1. Unit Testing Framework

```typescript
interface UnitTestingStrategy {
  framework: 'vitest';
  coverage: {
    threshold: 100;
    reporters: ['text', 'html', 'lcov'];
    exclude: ['**/*.d.ts', '**/*.config.*'];
  };
  
  testTypes: {
    components: 'render-and-interaction-tests';
    utilities: 'pure-function-tests';
    hooks: 'custom-hook-tests';
    lifecycle: 'micro-frontend-lifecycle-tests';
  };
  
  mocking: {
    external: 'vi.mock';
    modules: 'vi.doMock';
    timers: 'vi.useFakeTimers';
  };
}
```

### 2. Integration Testing Strategy

```typescript
interface IntegrationTestingStrategy {
  scope: 'micro-frontend-integration';
  
  testScenarios: {
    lifecycle: 'bootstrap-mount-unmount-flow';
    communication: 'inter-app-message-passing';
    routing: 'navigation-between-applications';
    stateManagement: 'shared-state-synchronization';
  };
  
  environment: {
    setup: 'multi-application-test-harness';
    cleanup: 'automatic-teardown';
    isolation: 'separate-test-contexts';
  };
}
```

### 3. End-to-End Testing Strategy

```typescript
interface E2ETestingStrategy {
  framework: 'playwright';
  browsers: ['chromium', 'firefox', 'webkit'];
  
  testScenarios: {
    userWorkflows: 'complete-user-journeys';
    crossApplication: 'navigation-and-data-flow';
    performance: 'loading-and-interaction-metrics';
    accessibility: 'a11y-compliance-testing';
  };
  
  infrastructure: {
    testServer: 'concurrent-application-serving';
    dataSetup: 'test-data-seeding';
    reporting: 'visual-test-reports';
  };
}
```

## Implementation Phases

### Phase 1: Structure Standardization (Week 1)

1. **Directory Cleanup**
   - Remove redundant `apps/examples/` directory
   - Consolidate duplicate functionality
   - Standardize directory naming conventions

2. **Configuration Alignment**
   - Standardize all `package.json` files
   - Align `vite.config.ts` configurations
   - Unify `tsconfig.json` settings

3. **Dependency Management**
   - Update all workspace references to `workspace:*`
   - Align external dependency versions
   - Remove unused dependencies

### Phase 2: Code Quality Enhancement (Week 2)

1. **Lifecycle Implementation**
   - Implement standard lifecycle hooks in all sub-applications
   - Add proper error handling and cleanup
   - Ensure consistent prop handling

2. **Framework Integration**
   - Verify proper adapter usage
   - Implement framework-specific best practices
   - Add performance optimizations

3. **Build Optimization**
   - Optimize bundle sizes
   - Implement code splitting
   - Add resource optimization

### Phase 3: Testing Infrastructure (Week 3)

1. **Unit Testing Setup**
   - Configure Vitest for all applications
   - Create shared test utilities
   - Implement component testing

2. **Integration Testing**
   - Set up micro-frontend integration tests
   - Test lifecycle interactions
   - Verify communication mechanisms

3. **E2E Testing**
   - Configure Playwright
   - Create user workflow tests
   - Add performance benchmarks

### Phase 4: Documentation and Examples (Week 4)

1. **Documentation Enhancement**
   - Create comprehensive README files
   - Add inline code documentation
   - Create usage examples

2. **Example Applications**
   - Enhance existing examples
   - Add new use case demonstrations
   - Create tutorial content

3. **Developer Experience**
   - Optimize development workflows
   - Add debugging tools
   - Create development guides

## Performance Considerations

### 1. Bundle Optimization

- **Code Splitting**: Implement dynamic imports for route-based splitting
- **Tree Shaking**: Ensure unused code elimination
- **Shared Dependencies**: Optimize shared library loading
- **Asset Optimization**: Compress images and optimize fonts

### 2. Runtime Performance

- **Lazy Loading**: Implement application lazy loading
- **Memory Management**: Proper cleanup in unmount lifecycle
- **Resource Caching**: Implement intelligent caching strategies
- **Performance Monitoring**: Add runtime performance metrics

### 3. Development Performance

- **Hot Module Replacement**: Optimize HMR for all frameworks
- **Build Speed**: Parallel builds and incremental compilation
- **Test Performance**: Fast test execution and parallel testing
- **Development Server**: Optimized development server configuration

## Security Considerations

### 1. Application Isolation

- **Sandbox Security**: Proper iframe and proxy sandbox implementation
- **CSS Isolation**: Prevent style bleeding between applications
- **JavaScript Isolation**: Secure global variable management
- **DOM Isolation**: Prevent DOM manipulation conflicts

### 2. Communication Security

- **Message Validation**: Validate inter-application messages
- **Origin Verification**: Verify message origins
- **Data Sanitization**: Sanitize shared data
- **Permission Management**: Implement permission-based communication

### 3. Build Security

- **Dependency Scanning**: Regular security audits
- **Code Analysis**: Static security analysis
- **Bundle Analysis**: Detect malicious code injection
- **Environment Security**: Secure build environment configuration